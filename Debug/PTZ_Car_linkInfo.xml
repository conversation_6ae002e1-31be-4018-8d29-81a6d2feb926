<?xml version="1.0" encoding="ISO-8859-1" ?>
<link_info>
   <banner>TI ARM Clang Linker PC v4.0.3.LTS</banner>
   <copyright>Copyright (c) 1996-2018 Texas Instruments Incorporated</copyright>
   <command_line>C:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\bin\tiarmlnk -IC:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib -o PTZ_Car.out -mPTZ_Car.map -iC:/ti/mspm0_sdk_2_04_00_06/source -iC:/Users/<USER>/workspace_ccstheia/PTZ_Car -iC:/Users/<USER>/workspace_ccstheia/PTZ_Car/Debug/syscfg -iC:/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib --diag_wrap=off --display_error_number --warn_sections --xml_link_info=PTZ_Car_linkInfo.xml --rom_model ./ti_msp_dl_config.o ./startup_mspm0g350x_ticlang.o ./main.o ./Hardware/Motor.o ./Hardware/pid.o ./Hardware/pid_app.o -l./device_linker.cmd -ldevice.cmd.genlibs -llibc.a --start-group -llibc++.a -llibc++abi.a -llibc.a -llibsys.a -llibsysbm.a -llibclang_rt.builtins.a -llibclang_rt.profile.a --end-group --cg_opt_level=2</command_line>
   <link_time>0x6889924b</link_time>
   <link_errors>0x0</link_errors>
   <output_file>C:\Users\<USER>\workspace_ccstheia\PTZ_Car\Debug\PTZ_Car.out</output_file>
   <entry_point>
      <name>_c_int00_noargs</name>
      <address>0x6f9</address>
   </entry_point>
   <input_file_list>
      <input_file id="fl-1">
         <path>C:\Users\<USER>\workspace_ccstheia\PTZ_Car\Debug\.\</path>
         <kind>object</kind>
         <file>ti_msp_dl_config.o</file>
         <name>ti_msp_dl_config.o</name>
      </input_file>
      <input_file id="fl-2">
         <path>C:\Users\<USER>\workspace_ccstheia\PTZ_Car\Debug\.\</path>
         <kind>object</kind>
         <file>startup_mspm0g350x_ticlang.o</file>
         <name>startup_mspm0g350x_ticlang.o</name>
      </input_file>
      <input_file id="fl-3">
         <path>C:\Users\<USER>\workspace_ccstheia\PTZ_Car\Debug\.\</path>
         <kind>object</kind>
         <file>main.o</file>
         <name>main.o</name>
      </input_file>
      <input_file id="fl-4">
         <path>C:\Users\<USER>\workspace_ccstheia\PTZ_Car\Debug\.\Hardware\</path>
         <kind>object</kind>
         <file>Motor.o</file>
         <name>Motor.o</name>
      </input_file>
      <input_file id="fl-5">
         <path>C:\Users\<USER>\workspace_ccstheia\PTZ_Car\Debug\.\Hardware\</path>
         <kind>object</kind>
         <file>pid.o</file>
         <name>pid.o</name>
      </input_file>
      <input_file id="fl-6">
         <path>C:\Users\<USER>\workspace_ccstheia\PTZ_Car\Debug\.\Hardware\</path>
         <kind>object</kind>
         <file>pid_app.o</file>
         <name>pid_app.o</name>
      </input_file>
      <input_file id="fl-13">
         <path>C:\Users\<USER>\workspace_ccstheia\PTZ_Car\Debug\</path>
         <kind>object</kind>
         <file>&lt;internal&gt;</file>
         <name>&lt;internal&gt;</name>
      </input_file>
      <input_file id="fl-14">
         <path>C:\ti\mspm0_sdk_2_04_00_06\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_common.o</name>
      </input_file>
      <input_file id="fl-15">
         <path>C:\ti\mspm0_sdk_2_04_00_06\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_timer.o</name>
      </input_file>
      <input_file id="fl-16">
         <path>C:\ti\mspm0_sdk_2_04_00_06\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_sysctl_mspm0g1x0x_g3x0x.o</name>
      </input_file>
      <input_file id="fl-2d">
         <path>C:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>boot_cortex_m.c.obj</name>
      </input_file>
      <input_file id="fl-2e">
         <path>C:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>mathacl_init.c.obj</name>
      </input_file>
      <input_file id="fl-2f">
         <path>C:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>autoinit.c.obj</name>
      </input_file>
      <input_file id="fl-30">
         <path>C:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>pre_init.c.obj</name>
      </input_file>
      <input_file id="fl-31">
         <path>C:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>cpy_tbl.c.obj</name>
      </input_file>
      <input_file id="fl-32">
         <path>C:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_zero_init.c.obj</name>
      </input_file>
      <input_file id="fl-33">
         <path>C:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_none.c.obj</name>
      </input_file>
      <input_file id="fl-34">
         <path>C:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_lzss.c.obj</name>
      </input_file>
      <input_file id="fl-35">
         <path>C:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>exit.c.obj</name>
      </input_file>
      <input_file id="fl-36">
         <path>C:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_lock.c.obj</name>
      </input_file>
      <input_file id="fl-37">
         <path>C:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>args_main.c.obj</name>
      </input_file>
      <input_file id="fl-d7">
         <path>C:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostexit.c.obj</name>
      </input_file>
      <input_file id="fl-d8">
         <path>C:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>addsf3.S.obj</name>
      </input_file>
      <input_file id="fl-d9">
         <path>C:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>mulsf3.S.obj</name>
      </input_file>
      <input_file id="fl-da">
         <path>C:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memcpy.S.obj</name>
      </input_file>
      <input_file id="fl-db">
         <path>C:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memset.S.obj</name>
      </input_file>
      <input_file id="fl-dc">
         <path>C:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>muldsi3.S.obj</name>
      </input_file>
      <input_file id="fl-dd">
         <path>C:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memcpy16.S.obj</name>
      </input_file>
      <input_file id="fl-de">
         <path>C:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memset16.S.obj</name>
      </input_file>
   </input_file_list>
   <object_component_list>
      <object_component id="oc-18">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-da">
         <name>.text.DL_Timer_initFourCCPWMMode</name>
         <load_address>0xc0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc0</run_address>
         <size>0x104</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-d1">
         <name>.text.DL_SYSCTL_configSYSPLL</name>
         <load_address>0x1c4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1c4</run_address>
         <size>0xdc</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-34">
         <name>.text.GROUP1_IRQHandler</name>
         <load_address>0x2a0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2a0</run_address>
         <size>0xc0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-7d">
         <name>.text:memcpy</name>
         <load_address>0x360</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x360</run_address>
         <size>0x9a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
      <object_component id="oc-33">
         <name>.text.Default_Handler</name>
         <load_address>0x3fa</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x3fa</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-af">
         <name>.text.SYSCFG_DL_Motor_init</name>
         <load_address>0x3fc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x3fc</run_address>
         <size>0x90</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-1c">
         <name>.text:decompress:lzss</name>
         <load_address>0x48c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x48c</run_address>
         <size>0x7c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-ac">
         <name>.text.SYSCFG_DL_GPIO_init</name>
         <load_address>0x508</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x508</run_address>
         <size>0x78</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-ad">
         <name>.text.SYSCFG_DL_SYSCTL_init</name>
         <load_address>0x580</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x580</run_address>
         <size>0x6c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-87">
         <name>.text.Set_PWM_Duty</name>
         <load_address>0x5ec</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x5ec</run_address>
         <size>0x58</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-d7">
         <name>.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK</name>
         <load_address>0x644</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x644</run_address>
         <size>0x44</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-92">
         <name>.text:__TI_auto_init_nobinit_nopinit</name>
         <load_address>0x688</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x688</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-ab">
         <name>.text.SYSCFG_DL_initPower</name>
         <load_address>0x6c4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6c4</run_address>
         <size>0x34</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-54">
         <name>.text:_c_int00_noargs</name>
         <load_address>0x6f8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6f8</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-dc">
         <name>.text.DL_Timer_setCaptCompUpdateMethod</name>
         <load_address>0x720</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x720</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-d9">
         <name>.text.DL_Timer_setClockConfig</name>
         <load_address>0x73c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x73c</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-db">
         <name>.text.DL_Timer_setCaptureCompareOutCtl</name>
         <load_address>0x758</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x758</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-82">
         <name>.text.SYSCFG_DL_init</name>
         <load_address>0x770</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x770</run_address>
         <size>0x18</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-6d">
         <name>.text.main</name>
         <load_address>0x788</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x788</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-4b">
         <name>.text:decompress:ZI:__TI_zero_init_nomemset</name>
         <load_address>0x7a0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x7a0</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-20">
         <name>.text:decompress:none</name>
         <load_address>0x7b6</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x7b6</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-b3">
         <name>.text.DL_Timer_setCaptureCompareValue</name>
         <load_address>0x7c8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x7c8</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-cd">
         <name>.text.DL_Common_delayCycles</name>
         <load_address>0x7d8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x7d8</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-b0">
         <name>.text.SYSCFG_DL_SYSTICK_init</name>
         <load_address>0x7e2</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x7e2</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-44">
         <name>.text.__aeabi_memcpy</name>
         <load_address>0x7e4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x7e4</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-da"/>
      </object_component>
      <object_component id="oc-8b">
         <name>.text:abort</name>
         <load_address>0x7ec</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x7ec</run_address>
         <size>0x6</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-bc">
         <name>.text.HOSTexit</name>
         <load_address>0x7f2</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x7f2</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-d7"/>
      </object_component>
      <object_component id="oc-2f">
         <name>.text.Reset_Handler</name>
         <load_address>0x7f6</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x7f6</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-69">
         <name>.text._system_pre_init</name>
         <load_address>0x7fa</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x7fa</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-130">
         <name>__TI_handler_table</name>
         <load_address>0x838</load_address>
         <readonly>true</readonly>
         <run_address>0x838</run_address>
         <size>0xc</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-133">
         <name>.cinit..bss.load</name>
         <load_address>0x844</load_address>
         <readonly>true</readonly>
         <run_address>0x844</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-132">
         <name>.cinit..data.load</name>
         <load_address>0x84c</load_address>
         <readonly>true</readonly>
         <run_address>0x84c</run_address>
         <size>0x7</size>
         <alignment>0x1</alignment>
      </object_component>
      <object_component id="oc-131">
         <name>__TI_cinit_table</name>
         <load_address>0x854</load_address>
         <readonly>true</readonly>
         <run_address>0x854</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-d8">
         <name>.rodata.gSYSPLLConfig</name>
         <load_address>0x800</load_address>
         <readonly>true</readonly>
         <run_address>0x800</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-de">
         <name>.rodata.gMotorConfig</name>
         <load_address>0x828</load_address>
         <readonly>true</readonly>
         <run_address>0x828</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-dd">
         <name>.rodata.gMotorClockConfig</name>
         <load_address>0x830</load_address>
         <readonly>true</readonly>
         <run_address>0x830</run_address>
         <size>0x3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-fa">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-5c">
         <name>.data.gEncoderCount_L</name>
         <load_address>0x20200000</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200000</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-5d">
         <name>.data.gEncoderCount_R</name>
         <load_address>0x20200004</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200004</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-5b">
         <name>.common:gpioB</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200008</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-1a">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20207e00</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-135">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20207e00</run_address>
         <size>0x0</size>
         <alignment>0x8</alignment>
      </object_component>
      <object_component id="oc-ae">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x82</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-bb">
         <name>.debug_loc</name>
         <load_address>0x82</load_address>
         <run_address>0x82</run_address>
         <size>0x3e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-ee">
         <name>.debug_loc</name>
         <load_address>0xc0</load_address>
         <run_address>0xc0</run_address>
         <size>0x13</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-b5">
         <name>.debug_loc</name>
         <load_address>0xd3</load_address>
         <run_address>0xd3</run_address>
         <size>0x1a27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-d5">
         <name>.debug_loc</name>
         <load_address>0x1afa</load_address>
         <run_address>0x1afa</run_address>
         <size>0x414</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-3f">
         <name>.debug_loc</name>
         <load_address>0x1f0e</load_address>
         <run_address>0x1f0e</run_address>
         <size>0xd8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-93">
         <name>.debug_loc</name>
         <load_address>0x1fe6</load_address>
         <run_address>0x1fe6</run_address>
         <size>0x424</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-4c">
         <name>.debug_loc</name>
         <load_address>0x240a</load_address>
         <run_address>0x240a</run_address>
         <size>0x16c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-4a">
         <name>.debug_loc</name>
         <load_address>0x2576</load_address>
         <run_address>0x2576</run_address>
         <size>0x6f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-42">
         <name>.debug_loc</name>
         <load_address>0x25e5</load_address>
         <run_address>0x25e5</run_address>
         <size>0x167</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-91">
         <name>.debug_loc</name>
         <load_address>0x274c</load_address>
         <run_address>0x274c</run_address>
         <size>0x26</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-b1">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1f5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-39">
         <name>.debug_abbrev</name>
         <load_address>0x1f5</load_address>
         <run_address>0x1f5</run_address>
         <size>0x6d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-5e">
         <name>.debug_abbrev</name>
         <load_address>0x262</load_address>
         <run_address>0x262</run_address>
         <size>0x177</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-b9">
         <name>.debug_abbrev</name>
         <load_address>0x3d9</load_address>
         <run_address>0x3d9</run_address>
         <size>0xef</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-ec">
         <name>.debug_abbrev</name>
         <load_address>0x4c8</load_address>
         <run_address>0x4c8</run_address>
         <size>0x62</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-df">
         <name>.debug_abbrev</name>
         <load_address>0x52a</load_address>
         <run_address>0x52a</run_address>
         <size>0x286</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-ef">
         <name>.debug_abbrev</name>
         <load_address>0x7b0</load_address>
         <run_address>0x7b0</run_address>
         <size>0x218</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-3b">
         <name>.debug_abbrev</name>
         <load_address>0x9c8</load_address>
         <run_address>0x9c8</run_address>
         <size>0xaf</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-c6">
         <name>.debug_abbrev</name>
         <load_address>0xa77</load_address>
         <run_address>0xa77</run_address>
         <size>0x170</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-80">
         <name>.debug_abbrev</name>
         <load_address>0xbe7</load_address>
         <run_address>0xbe7</run_address>
         <size>0x39</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-62">
         <name>.debug_abbrev</name>
         <load_address>0xc20</load_address>
         <run_address>0xc20</run_address>
         <size>0xc2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-48">
         <name>.debug_abbrev</name>
         <load_address>0xce2</load_address>
         <run_address>0xce2</run_address>
         <size>0x70</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-40">
         <name>.debug_abbrev</name>
         <load_address>0xd52</load_address>
         <run_address>0xd52</run_address>
         <size>0x8d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-c0">
         <name>.debug_abbrev</name>
         <load_address>0xddf</load_address>
         <run_address>0xddf</run_address>
         <size>0x98</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-e1">
         <name>.debug_abbrev</name>
         <load_address>0xe77</load_address>
         <run_address>0xe77</run_address>
         <size>0x2c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d7"/>
      </object_component>
      <object_component id="oc-61">
         <name>.debug_abbrev</name>
         <load_address>0xea3</load_address>
         <run_address>0xea3</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-da"/>
      </object_component>
      <object_component id="oc-a4">
         <name>.debug_abbrev</name>
         <load_address>0xeca</load_address>
         <run_address>0xeca</run_address>
         <size>0x25</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
      <object_component id="oc-137">
         <name>.debug_abbrev</name>
         <load_address>0xeef</load_address>
         <run_address>0xeef</run_address>
         <size>0xf</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-86">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x23cc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-19">
         <name>.debug_info</name>
         <load_address>0x23cc</load_address>
         <run_address>0x23cc</run_address>
         <size>0x80</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-37">
         <name>.debug_info</name>
         <load_address>0x244c</load_address>
         <run_address>0x244c</run_address>
         <size>0xada</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-8a">
         <name>.debug_info</name>
         <load_address>0x2f26</load_address>
         <run_address>0x2f26</run_address>
         <size>0xd3d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-ce">
         <name>.debug_info</name>
         <load_address>0x3c63</load_address>
         <run_address>0x3c63</run_address>
         <size>0x75</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-b4">
         <name>.debug_info</name>
         <load_address>0x3cd8</load_address>
         <run_address>0x3cd8</run_address>
         <size>0x3172</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-d3">
         <name>.debug_info</name>
         <load_address>0x6e4a</load_address>
         <run_address>0x6e4a</run_address>
         <size>0x1090</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-1b">
         <name>.debug_info</name>
         <load_address>0x7eda</load_address>
         <run_address>0x7eda</run_address>
         <size>0x423</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-94">
         <name>.debug_info</name>
         <load_address>0x82fd</load_address>
         <run_address>0x82fd</run_address>
         <size>0x744</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-6a">
         <name>.debug_info</name>
         <load_address>0x8a41</load_address>
         <run_address>0x8a41</run_address>
         <size>0x46</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-4e">
         <name>.debug_info</name>
         <load_address>0x8a87</load_address>
         <run_address>0x8a87</run_address>
         <size>0x192</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-22">
         <name>.debug_info</name>
         <load_address>0x8c19</load_address>
         <run_address>0x8c19</run_address>
         <size>0xc6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-1d">
         <name>.debug_info</name>
         <load_address>0x8cdf</load_address>
         <run_address>0x8cdf</run_address>
         <size>0x17c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-8e">
         <name>.debug_info</name>
         <load_address>0x8e5b</load_address>
         <run_address>0x8e5b</run_address>
         <size>0xf8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-bd">
         <name>.debug_info</name>
         <load_address>0x8f53</load_address>
         <run_address>0x8f53</run_address>
         <size>0x3b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d7"/>
      </object_component>
      <object_component id="oc-47">
         <name>.debug_info</name>
         <load_address>0x8f8e</load_address>
         <run_address>0x8f8e</run_address>
         <size>0x199</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-da"/>
      </object_component>
      <object_component id="oc-7b">
         <name>.debug_info</name>
         <load_address>0x9127</load_address>
         <run_address>0x9127</run_address>
         <size>0x2fa</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
      <object_component id="oc-136">
         <name>.debug_info</name>
         <load_address>0x9421</load_address>
         <run_address>0x9421</run_address>
         <size>0xa3</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-84">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x38</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-30">
         <name>.debug_ranges</name>
         <load_address>0x38</load_address>
         <run_address>0x38</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-36">
         <name>.debug_ranges</name>
         <load_address>0x50</load_address>
         <run_address>0x50</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-b6">
         <name>.debug_ranges</name>
         <load_address>0x68</load_address>
         <run_address>0x68</run_address>
         <size>0x1d8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-d2">
         <name>.debug_ranges</name>
         <load_address>0x240</load_address>
         <run_address>0x240</run_address>
         <size>0x1a8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-3e">
         <name>.debug_ranges</name>
         <load_address>0x3e8</load_address>
         <run_address>0x3e8</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-96">
         <name>.debug_ranges</name>
         <load_address>0x430</load_address>
         <run_address>0x430</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-50">
         <name>.debug_ranges</name>
         <load_address>0x478</load_address>
         <run_address>0x478</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-43">
         <name>.debug_ranges</name>
         <load_address>0x490</load_address>
         <run_address>0x490</run_address>
         <size>0x50</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-8d">
         <name>.debug_ranges</name>
         <load_address>0x4e0</load_address>
         <run_address>0x4e0</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-7c">
         <name>.debug_ranges</name>
         <load_address>0x4f8</load_address>
         <run_address>0x4f8</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
      <object_component id="oc-b2">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1e20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-3a">
         <name>.debug_str</name>
         <load_address>0x1e20</load_address>
         <run_address>0x1e20</run_address>
         <size>0x15a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-5f">
         <name>.debug_str</name>
         <load_address>0x1f7a</load_address>
         <run_address>0x1f7a</run_address>
         <size>0x766</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-ba">
         <name>.debug_str</name>
         <load_address>0x26e0</load_address>
         <run_address>0x26e0</run_address>
         <size>0x6cc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-ed">
         <name>.debug_str</name>
         <load_address>0x2dac</load_address>
         <run_address>0x2dac</run_address>
         <size>0x16d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-e0">
         <name>.debug_str</name>
         <load_address>0x2f19</load_address>
         <run_address>0x2f19</run_address>
         <size>0x1dcb</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-f0">
         <name>.debug_str</name>
         <load_address>0x4ce4</load_address>
         <run_address>0x4ce4</run_address>
         <size>0x1075</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-3c">
         <name>.debug_str</name>
         <load_address>0x5d59</load_address>
         <run_address>0x5d59</run_address>
         <size>0x225</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-c7">
         <name>.debug_str</name>
         <load_address>0x5f7e</load_address>
         <run_address>0x5f7e</run_address>
         <size>0x32f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-81">
         <name>.debug_str</name>
         <load_address>0x62ad</load_address>
         <run_address>0x62ad</run_address>
         <size>0xf5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-63">
         <name>.debug_str</name>
         <load_address>0x63a2</load_address>
         <run_address>0x63a2</run_address>
         <size>0x19b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-49">
         <name>.debug_str</name>
         <load_address>0x653d</load_address>
         <run_address>0x653d</run_address>
         <size>0x168</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-41">
         <name>.debug_str</name>
         <load_address>0x66a5</load_address>
         <run_address>0x66a5</run_address>
         <size>0x1d5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-c1">
         <name>.debug_str</name>
         <load_address>0x687a</load_address>
         <run_address>0x687a</run_address>
         <size>0x148</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-e2">
         <name>.debug_str</name>
         <load_address>0x69c2</load_address>
         <run_address>0x69c2</run_address>
         <size>0xe9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d7"/>
      </object_component>
      <object_component id="oc-85">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xa4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-31">
         <name>.debug_frame</name>
         <load_address>0xa4</load_address>
         <run_address>0xa4</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-38">
         <name>.debug_frame</name>
         <load_address>0xd4</load_address>
         <run_address>0xd4</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-88">
         <name>.debug_frame</name>
         <load_address>0x114</load_address>
         <run_address>0x114</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-d0">
         <name>.debug_frame</name>
         <load_address>0x144</load_address>
         <run_address>0x144</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-b7">
         <name>.debug_frame</name>
         <load_address>0x164</load_address>
         <run_address>0x164</run_address>
         <size>0x408</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-d4">
         <name>.debug_frame</name>
         <load_address>0x56c</load_address>
         <run_address>0x56c</run_address>
         <size>0x12c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-53">
         <name>.debug_frame</name>
         <load_address>0x698</load_address>
         <run_address>0x698</run_address>
         <size>0x90</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-97">
         <name>.debug_frame</name>
         <load_address>0x728</load_address>
         <run_address>0x728</run_address>
         <size>0x100</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-6c">
         <name>.debug_frame</name>
         <load_address>0x828</load_address>
         <run_address>0x828</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-4f">
         <name>.debug_frame</name>
         <load_address>0x848</load_address>
         <run_address>0x848</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-21">
         <name>.debug_frame</name>
         <load_address>0x880</load_address>
         <run_address>0x880</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-1f">
         <name>.debug_frame</name>
         <load_address>0x8a8</load_address>
         <run_address>0x8a8</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-8c">
         <name>.debug_frame</name>
         <load_address>0x8d8</load_address>
         <run_address>0x8d8</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-be">
         <name>.debug_frame</name>
         <load_address>0x908</load_address>
         <run_address>0x908</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d7"/>
      </object_component>
      <object_component id="oc-83">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x517</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-32">
         <name>.debug_line</name>
         <load_address>0x517</load_address>
         <run_address>0x517</run_address>
         <size>0xb8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-35">
         <name>.debug_line</name>
         <load_address>0x5cf</load_address>
         <run_address>0x5cf</run_address>
         <size>0x313</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-89">
         <name>.debug_line</name>
         <load_address>0x8e2</load_address>
         <run_address>0x8e2</run_address>
         <size>0x1e1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-cf">
         <name>.debug_line</name>
         <load_address>0xac3</load_address>
         <run_address>0xac3</run_address>
         <size>0x178</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-b8">
         <name>.debug_line</name>
         <load_address>0xc3b</load_address>
         <run_address>0xc3b</run_address>
         <size>0x176e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-d6">
         <name>.debug_line</name>
         <load_address>0x23a9</load_address>
         <run_address>0x23a9</run_address>
         <size>0x982</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-3d">
         <name>.debug_line</name>
         <load_address>0x2d2b</load_address>
         <run_address>0x2d2b</run_address>
         <size>0x1dc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-95">
         <name>.debug_line</name>
         <load_address>0x2f07</load_address>
         <run_address>0x2f07</run_address>
         <size>0x51a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-6b">
         <name>.debug_line</name>
         <load_address>0x3421</load_address>
         <run_address>0x3421</run_address>
         <size>0x3e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-4d">
         <name>.debug_line</name>
         <load_address>0x345f</load_address>
         <run_address>0x345f</run_address>
         <size>0xfe</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-23">
         <name>.debug_line</name>
         <load_address>0x355d</load_address>
         <run_address>0x355d</run_address>
         <size>0xc0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-1e">
         <name>.debug_line</name>
         <load_address>0x361d</load_address>
         <run_address>0x361d</run_address>
         <size>0x1c8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-8f">
         <name>.debug_line</name>
         <load_address>0x37e5</load_address>
         <run_address>0x37e5</run_address>
         <size>0x67</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-bf">
         <name>.debug_line</name>
         <load_address>0x384c</load_address>
         <run_address>0x384c</run_address>
         <size>0x41</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d7"/>
      </object_component>
      <object_component id="oc-46">
         <name>.debug_line</name>
         <load_address>0x388d</load_address>
         <run_address>0x388d</run_address>
         <size>0xa4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-da"/>
      </object_component>
      <object_component id="oc-7a">
         <name>.debug_line</name>
         <load_address>0x3931</load_address>
         <run_address>0x3931</run_address>
         <size>0xa0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
      <object_component id="oc-45">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-da"/>
      </object_component>
      <object_component id="oc-79">
         <name>.debug_aranges</name>
         <load_address>0x20</load_address>
         <run_address>0x20</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
   </object_component_list>
   <logical_group_list>
      <logical_group id="lg-2" display="no" color="cyan">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <contents>
            <object_component_ref idref="oc-18"/>
         </contents>
      </logical_group>
      <logical_group id="lg-3" display="no" color="cyan">
         <name>.text</name>
         <load_address>0xc0</load_address>
         <run_address>0xc0</run_address>
         <size>0x740</size>
         <contents>
            <object_component_ref idref="oc-da"/>
            <object_component_ref idref="oc-d1"/>
            <object_component_ref idref="oc-34"/>
            <object_component_ref idref="oc-7d"/>
            <object_component_ref idref="oc-33"/>
            <object_component_ref idref="oc-af"/>
            <object_component_ref idref="oc-1c"/>
            <object_component_ref idref="oc-ac"/>
            <object_component_ref idref="oc-ad"/>
            <object_component_ref idref="oc-87"/>
            <object_component_ref idref="oc-d7"/>
            <object_component_ref idref="oc-92"/>
            <object_component_ref idref="oc-ab"/>
            <object_component_ref idref="oc-54"/>
            <object_component_ref idref="oc-dc"/>
            <object_component_ref idref="oc-d9"/>
            <object_component_ref idref="oc-db"/>
            <object_component_ref idref="oc-82"/>
            <object_component_ref idref="oc-6d"/>
            <object_component_ref idref="oc-4b"/>
            <object_component_ref idref="oc-20"/>
            <object_component_ref idref="oc-b3"/>
            <object_component_ref idref="oc-cd"/>
            <object_component_ref idref="oc-b0"/>
            <object_component_ref idref="oc-44"/>
            <object_component_ref idref="oc-8b"/>
            <object_component_ref idref="oc-bc"/>
            <object_component_ref idref="oc-2f"/>
            <object_component_ref idref="oc-69"/>
         </contents>
      </logical_group>
      <logical_group id="lg-4" display="no" color="cyan">
         <name>.const</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-5" display="no" color="cyan">
         <name>.cinit</name>
         <load_address>0x838</load_address>
         <run_address>0x838</run_address>
         <size>0x30</size>
         <contents>
            <object_component_ref idref="oc-130"/>
            <object_component_ref idref="oc-133"/>
            <object_component_ref idref="oc-132"/>
            <object_component_ref idref="oc-131"/>
         </contents>
      </logical_group>
      <logical_group id="lg-6" display="no" color="cyan">
         <name>.pinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-7" display="no" color="cyan">
         <name>.rodata</name>
         <load_address>0x800</load_address>
         <run_address>0x800</run_address>
         <size>0x38</size>
         <contents>
            <object_component_ref idref="oc-d8"/>
            <object_component_ref idref="oc-de"/>
            <object_component_ref idref="oc-dd"/>
         </contents>
      </logical_group>
      <logical_group id="lg-8" display="no" color="cyan">
         <name>.ARM.exidx</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-9" display="no" color="cyan">
         <name>.init_array</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-a" display="no" color="cyan">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
            <object_component_ref idref="oc-fa"/>
         </contents>
      </logical_group>
      <logical_group id="lg-b" display="no" color="cyan">
         <name>.TI.ramfunc</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-c" display="no" color="cyan">
         <name>.vtable</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-d" display="no" color="cyan">
         <name>.args</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-e" display="no" color="cyan">
         <name>.data</name>
         <run_address>0x20200000</run_address>
         <size>0x8</size>
         <contents>
            <object_component_ref idref="oc-5c"/>
            <object_component_ref idref="oc-5d"/>
         </contents>
      </logical_group>
      <logical_group id="lg-f" display="no" color="cyan">
         <name>.bss</name>
         <run_address>0x20200008</run_address>
         <size>0x4</size>
         <contents>
            <object_component_ref idref="oc-5b"/>
         </contents>
      </logical_group>
      <logical_group id="lg-10" display="no" color="cyan">
         <name>.sysmem</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-11" display="no" color="cyan">
         <name>.stack</name>
         <run_address>0x20207e00</run_address>
         <size>0x200</size>
         <contents>
            <object_component_ref idref="oc-1a"/>
            <object_component_ref idref="oc-135"/>
         </contents>
      </logical_group>
      <logical_group id="lg-12" display="no" color="cyan">
         <name>.BCRConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-13" display="no" color="cyan">
         <name>.BSLConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-f1" display="no" color="cyan">
         <name>.TI.noinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-f2" display="no" color="cyan">
         <name>.TI.persistent</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-f3" display="no" color="cyan">
         <name>.TI.local</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-f4" display="no" color="cyan">
         <name>.TI.onchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-f5" display="no" color="cyan">
         <name>.TI.offchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-f6" display="no" color="cyan">
         <name>__llvm_prf_cnts</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-f8" display="no" color="cyan">
         <name>__llvm_prf_bits</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-114" display="never" color="cyan">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x2772</size>
         <contents>
            <object_component_ref idref="oc-ae"/>
            <object_component_ref idref="oc-bb"/>
            <object_component_ref idref="oc-ee"/>
            <object_component_ref idref="oc-b5"/>
            <object_component_ref idref="oc-d5"/>
            <object_component_ref idref="oc-3f"/>
            <object_component_ref idref="oc-93"/>
            <object_component_ref idref="oc-4c"/>
            <object_component_ref idref="oc-4a"/>
            <object_component_ref idref="oc-42"/>
            <object_component_ref idref="oc-91"/>
         </contents>
      </logical_group>
      <logical_group id="lg-116" display="never" color="cyan">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xefe</size>
         <contents>
            <object_component_ref idref="oc-b1"/>
            <object_component_ref idref="oc-39"/>
            <object_component_ref idref="oc-5e"/>
            <object_component_ref idref="oc-b9"/>
            <object_component_ref idref="oc-ec"/>
            <object_component_ref idref="oc-df"/>
            <object_component_ref idref="oc-ef"/>
            <object_component_ref idref="oc-3b"/>
            <object_component_ref idref="oc-c6"/>
            <object_component_ref idref="oc-80"/>
            <object_component_ref idref="oc-62"/>
            <object_component_ref idref="oc-48"/>
            <object_component_ref idref="oc-40"/>
            <object_component_ref idref="oc-c0"/>
            <object_component_ref idref="oc-e1"/>
            <object_component_ref idref="oc-61"/>
            <object_component_ref idref="oc-a4"/>
            <object_component_ref idref="oc-137"/>
         </contents>
      </logical_group>
      <logical_group id="lg-118" display="never" color="cyan">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x94c4</size>
         <contents>
            <object_component_ref idref="oc-86"/>
            <object_component_ref idref="oc-19"/>
            <object_component_ref idref="oc-37"/>
            <object_component_ref idref="oc-8a"/>
            <object_component_ref idref="oc-ce"/>
            <object_component_ref idref="oc-b4"/>
            <object_component_ref idref="oc-d3"/>
            <object_component_ref idref="oc-1b"/>
            <object_component_ref idref="oc-94"/>
            <object_component_ref idref="oc-6a"/>
            <object_component_ref idref="oc-4e"/>
            <object_component_ref idref="oc-22"/>
            <object_component_ref idref="oc-1d"/>
            <object_component_ref idref="oc-8e"/>
            <object_component_ref idref="oc-bd"/>
            <object_component_ref idref="oc-47"/>
            <object_component_ref idref="oc-7b"/>
            <object_component_ref idref="oc-136"/>
         </contents>
      </logical_group>
      <logical_group id="lg-11a" display="never" color="cyan">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x520</size>
         <contents>
            <object_component_ref idref="oc-84"/>
            <object_component_ref idref="oc-30"/>
            <object_component_ref idref="oc-36"/>
            <object_component_ref idref="oc-b6"/>
            <object_component_ref idref="oc-d2"/>
            <object_component_ref idref="oc-3e"/>
            <object_component_ref idref="oc-96"/>
            <object_component_ref idref="oc-50"/>
            <object_component_ref idref="oc-43"/>
            <object_component_ref idref="oc-8d"/>
            <object_component_ref idref="oc-7c"/>
         </contents>
      </logical_group>
      <logical_group id="lg-11c" display="never" color="cyan">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x6aab</size>
         <contents>
            <object_component_ref idref="oc-b2"/>
            <object_component_ref idref="oc-3a"/>
            <object_component_ref idref="oc-5f"/>
            <object_component_ref idref="oc-ba"/>
            <object_component_ref idref="oc-ed"/>
            <object_component_ref idref="oc-e0"/>
            <object_component_ref idref="oc-f0"/>
            <object_component_ref idref="oc-3c"/>
            <object_component_ref idref="oc-c7"/>
            <object_component_ref idref="oc-81"/>
            <object_component_ref idref="oc-63"/>
            <object_component_ref idref="oc-49"/>
            <object_component_ref idref="oc-41"/>
            <object_component_ref idref="oc-c1"/>
            <object_component_ref idref="oc-e2"/>
         </contents>
      </logical_group>
      <logical_group id="lg-11e" display="never" color="cyan">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x928</size>
         <contents>
            <object_component_ref idref="oc-85"/>
            <object_component_ref idref="oc-31"/>
            <object_component_ref idref="oc-38"/>
            <object_component_ref idref="oc-88"/>
            <object_component_ref idref="oc-d0"/>
            <object_component_ref idref="oc-b7"/>
            <object_component_ref idref="oc-d4"/>
            <object_component_ref idref="oc-53"/>
            <object_component_ref idref="oc-97"/>
            <object_component_ref idref="oc-6c"/>
            <object_component_ref idref="oc-4f"/>
            <object_component_ref idref="oc-21"/>
            <object_component_ref idref="oc-1f"/>
            <object_component_ref idref="oc-8c"/>
            <object_component_ref idref="oc-be"/>
         </contents>
      </logical_group>
      <logical_group id="lg-120" display="never" color="cyan">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x39d1</size>
         <contents>
            <object_component_ref idref="oc-83"/>
            <object_component_ref idref="oc-32"/>
            <object_component_ref idref="oc-35"/>
            <object_component_ref idref="oc-89"/>
            <object_component_ref idref="oc-cf"/>
            <object_component_ref idref="oc-b8"/>
            <object_component_ref idref="oc-d6"/>
            <object_component_ref idref="oc-3d"/>
            <object_component_ref idref="oc-95"/>
            <object_component_ref idref="oc-6b"/>
            <object_component_ref idref="oc-4d"/>
            <object_component_ref idref="oc-23"/>
            <object_component_ref idref="oc-1e"/>
            <object_component_ref idref="oc-8f"/>
            <object_component_ref idref="oc-bf"/>
            <object_component_ref idref="oc-46"/>
            <object_component_ref idref="oc-7a"/>
         </contents>
      </logical_group>
      <logical_group id="lg-12a" display="never" color="cyan">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x48</size>
         <contents>
            <object_component_ref idref="oc-45"/>
            <object_component_ref idref="oc-79"/>
         </contents>
      </logical_group>
      <logical_group id="lg-134" display="no" color="cyan">
         <name>Veneer$$CMSE</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <load_segment id="lg-13c" display="no" color="cyan">
         <name>SEGMENT_0</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x868</size>
         <flags>0x5</flags>
         <contents>
            <logical_group_ref idref="lg-2"/>
            <logical_group_ref idref="lg-3"/>
            <logical_group_ref idref="lg-7"/>
            <logical_group_ref idref="lg-5"/>
         </contents>
      </load_segment>
      <load_segment id="lg-13d" display="no" color="cyan">
         <name>SEGMENT_1</name>
         <run_address>0x20200000</run_address>
         <size>0xc</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-e"/>
            <logical_group_ref idref="lg-f"/>
         </contents>
      </load_segment>
      <load_segment id="lg-13e" display="no" color="cyan">
         <name>SEGMENT_2</name>
         <run_address>0x20207e00</run_address>
         <size>0x200</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-11"/>
         </contents>
      </load_segment>
   </logical_group_list>
   <placement_map>
      <memory_area display="yes" color="green">
         <name>FLASH</name>
         <page_id>0x0</page_id>
         <origin>0x0</origin>
         <length>0x20000</length>
         <used_space>0x868</used_space>
         <unused_space>0x1f798</unused_space>
         <attributes>RX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-a"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0xc0</size>
               <logical_group_ref idref="lg-2"/>
            </allocated_space>
            <allocated_space>
               <start_address>0xc0</start_address>
               <size>0x740</size>
               <logical_group_ref idref="lg-3"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x800</start_address>
               <size>0x38</size>
               <logical_group_ref idref="lg-7"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x838</start_address>
               <size>0x30</size>
               <logical_group_ref idref="lg-5"/>
            </allocated_space>
            <available_space>
               <start_address>0x868</start_address>
               <size>0x1f798</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>SRAM</name>
         <page_id>0x0</page_id>
         <origin>0x20200000</origin>
         <length>0x8000</length>
         <used_space>0x20c</used_space>
         <unused_space>0x7df4</unused_space>
         <attributes>RWX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-f6"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-f8"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x8</size>
               <logical_group_ref idref="lg-e"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200008</start_address>
               <size>0x4</size>
               <logical_group_ref idref="lg-f"/>
            </allocated_space>
            <available_space>
               <start_address>0x2020000c</start_address>
               <size>0x7df4</size>
            </available_space>
            <allocated_space>
               <start_address>0x20207e00</start_address>
               <size>0x200</size>
               <logical_group_ref idref="lg-11"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BCR_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00000</origin>
         <length>0xff</length>
         <used_space>0x0</used_space>
         <unused_space>0xff</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BSL_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00100</origin>
         <length>0x80</length>
         <used_space>0x0</used_space>
         <unused_space>0x80</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
   </placement_map>
   <cptbl_list>
      <cptbl>
         <name>__TI_cinit_table</name>
         <cprec>
            <name>.bss</name>
            <load_address>0x844</load_address>
            <load_size>0x8</load_size>
            <run_address>0x20200008</run_address>
            <run_size>0x4</run_size>
            <compression>zero_init</compression>
         </cprec>
         <cprec>
            <name>.data</name>
            <load_address>0x84c</load_address>
            <load_size>0x7</load_size>
            <run_address>0x20200000</run_address>
            <run_size>0x8</run_size>
            <compression>lzss</compression>
         </cprec>
      </cptbl>
   </cptbl_list>
   <handler_table>
      <handler_table_name>__TI_handler_table</handler_table_name>
      <handler>
         <index>0x0</index>
         <name>__TI_decompress_lzss</name>
      </handler>
      <handler>
         <index>0x1</index>
         <name>__TI_decompress_none</name>
      </handler>
      <handler>
         <index>0x2</index>
         <name>__TI_zero_init</name>
      </handler>
   </handler_table>
   <symbol_table>
      <symbol id="sm-1">
         <name>__start___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-2">
         <name>__stop___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-3">
         <name>__start___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-4">
         <name>__stop___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-5">
         <name>__TI_CINIT_Base</name>
         <value>0x854</value>
      </symbol>
      <symbol id="sm-6">
         <name>__TI_CINIT_Limit</name>
         <value>0x864</value>
      </symbol>
      <symbol id="sm-7">
         <name>__TI_CINIT_Warm</name>
         <value>0x864</value>
      </symbol>
      <symbol id="sm-8">
         <name>__TI_Handler_Table_Base</name>
         <value>0x838</value>
      </symbol>
      <symbol id="sm-9">
         <name>__TI_Handler_Table_Limit</name>
         <value>0x844</value>
      </symbol>
      <symbol id="sm-a">
         <name>binit</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-b">
         <name>__binit__</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-c">
         <name>__STACK_SIZE</name>
         <value>0x200</value>
      </symbol>
      <symbol id="sm-d">
         <name>__STACK_END</name>
         <value>0x20208000</value>
      </symbol>
      <symbol id="sm-e">
         <name>__TI_pprof_out_hndl</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-f">
         <name>__TI_prof_data_start</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-10">
         <name>__TI_prof_data_size</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-50">
         <name>SYSCFG_DL_init</name>
         <value>0x771</value>
         <object_component_ref idref="oc-82"/>
      </symbol>
      <symbol id="sm-51">
         <name>SYSCFG_DL_initPower</name>
         <value>0x6c5</value>
         <object_component_ref idref="oc-ab"/>
      </symbol>
      <symbol id="sm-52">
         <name>SYSCFG_DL_GPIO_init</name>
         <value>0x509</value>
         <object_component_ref idref="oc-ac"/>
      </symbol>
      <symbol id="sm-53">
         <name>SYSCFG_DL_SYSCTL_init</name>
         <value>0x581</value>
         <object_component_ref idref="oc-ad"/>
      </symbol>
      <symbol id="sm-54">
         <name>SYSCFG_DL_Motor_init</name>
         <value>0x3fd</value>
         <object_component_ref idref="oc-af"/>
      </symbol>
      <symbol id="sm-55">
         <name>SYSCFG_DL_SYSTICK_init</name>
         <value>0x7e3</value>
         <object_component_ref idref="oc-b0"/>
      </symbol>
      <symbol id="sm-60">
         <name>Default_Handler</name>
         <value>0x3fb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-61">
         <name>Reset_Handler</name>
         <value>0x7f7</value>
         <object_component_ref idref="oc-2f"/>
      </symbol>
      <symbol id="sm-62">
         <name>interruptVectors</name>
         <value>0x0</value>
         <object_component_ref idref="oc-18"/>
      </symbol>
      <symbol id="sm-63">
         <name>NMI_Handler</name>
         <value>0x3fb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-64">
         <name>HardFault_Handler</name>
         <value>0x3fb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-65">
         <name>SVC_Handler</name>
         <value>0x3fb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-66">
         <name>PendSV_Handler</name>
         <value>0x3fb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-67">
         <name>SysTick_Handler</name>
         <value>0x3fb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-68">
         <name>GROUP0_IRQHandler</name>
         <value>0x3fb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-69">
         <name>TIMG8_IRQHandler</name>
         <value>0x3fb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-6a">
         <name>UART3_IRQHandler</name>
         <value>0x3fb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-6b">
         <name>ADC0_IRQHandler</name>
         <value>0x3fb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-6c">
         <name>ADC1_IRQHandler</name>
         <value>0x3fb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-6d">
         <name>CANFD0_IRQHandler</name>
         <value>0x3fb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-6e">
         <name>DAC0_IRQHandler</name>
         <value>0x3fb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-6f">
         <name>SPI0_IRQHandler</name>
         <value>0x3fb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-70">
         <name>SPI1_IRQHandler</name>
         <value>0x3fb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-71">
         <name>UART1_IRQHandler</name>
         <value>0x3fb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-72">
         <name>UART2_IRQHandler</name>
         <value>0x3fb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-73">
         <name>UART0_IRQHandler</name>
         <value>0x3fb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-74">
         <name>TIMG0_IRQHandler</name>
         <value>0x3fb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-75">
         <name>TIMG6_IRQHandler</name>
         <value>0x3fb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-76">
         <name>TIMA0_IRQHandler</name>
         <value>0x3fb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-77">
         <name>TIMA1_IRQHandler</name>
         <value>0x3fb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-78">
         <name>TIMG7_IRQHandler</name>
         <value>0x3fb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-79">
         <name>TIMG12_IRQHandler</name>
         <value>0x3fb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7a">
         <name>I2C0_IRQHandler</name>
         <value>0x3fb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7b">
         <name>I2C1_IRQHandler</name>
         <value>0x3fb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7c">
         <name>AES_IRQHandler</name>
         <value>0x3fb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7d">
         <name>RTC_IRQHandler</name>
         <value>0x3fb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7e">
         <name>DMA_IRQHandler</name>
         <value>0x3fb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8b">
         <name>main</name>
         <value>0x789</value>
         <object_component_ref idref="oc-6d"/>
      </symbol>
      <symbol id="sm-8c">
         <name>GROUP1_IRQHandler</name>
         <value>0x2a1</value>
         <object_component_ref idref="oc-34"/>
      </symbol>
      <symbol id="sm-8d">
         <name>gpioB</name>
         <value>0x20200008</value>
      </symbol>
      <symbol id="sm-8e">
         <name>gEncoderCount_L</name>
         <value>0x20200000</value>
         <object_component_ref idref="oc-5c"/>
      </symbol>
      <symbol id="sm-8f">
         <name>gEncoderCount_R</name>
         <value>0x20200004</value>
         <object_component_ref idref="oc-5d"/>
      </symbol>
      <symbol id="sm-99">
         <name>Set_PWM_Duty</name>
         <value>0x5ed</value>
         <object_component_ref idref="oc-87"/>
      </symbol>
      <symbol id="sm-9c">
         <name>__TI_ATRegion0_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-9d">
         <name>__TI_ATRegion0_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-9e">
         <name>__TI_ATRegion0_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-9f">
         <name>__TI_ATRegion1_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-a0">
         <name>__TI_ATRegion1_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-a1">
         <name>__TI_ATRegion1_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-a2">
         <name>__TI_ATRegion2_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-a3">
         <name>__TI_ATRegion2_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-a4">
         <name>__TI_ATRegion2_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-ad">
         <name>DL_Common_delayCycles</name>
         <value>0x7d9</value>
         <object_component_ref idref="oc-cd"/>
      </symbol>
      <symbol id="sm-c4">
         <name>DL_Timer_setClockConfig</name>
         <value>0x73d</value>
         <object_component_ref idref="oc-d9"/>
      </symbol>
      <symbol id="sm-c5">
         <name>DL_Timer_setCaptureCompareValue</name>
         <value>0x7c9</value>
         <object_component_ref idref="oc-b3"/>
      </symbol>
      <symbol id="sm-c6">
         <name>DL_Timer_setCaptCompUpdateMethod</name>
         <value>0x721</value>
         <object_component_ref idref="oc-dc"/>
      </symbol>
      <symbol id="sm-c7">
         <name>DL_Timer_setCaptureCompareOutCtl</name>
         <value>0x759</value>
         <object_component_ref idref="oc-db"/>
      </symbol>
      <symbol id="sm-c8">
         <name>DL_Timer_initFourCCPWMMode</name>
         <value>0xc1</value>
         <object_component_ref idref="oc-da"/>
      </symbol>
      <symbol id="sm-d6">
         <name>DL_SYSCTL_configSYSPLL</name>
         <value>0x1c5</value>
         <object_component_ref idref="oc-d1"/>
      </symbol>
      <symbol id="sm-d7">
         <name>DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK</name>
         <value>0x645</value>
         <object_component_ref idref="oc-d7"/>
      </symbol>
      <symbol id="sm-e2">
         <name>_c_int00_noargs</name>
         <value>0x6f9</value>
         <object_component_ref idref="oc-54"/>
      </symbol>
      <symbol id="sm-e3">
         <name>__stack</name>
         <value>0x20207e00</value>
         <object_component_ref idref="oc-1a"/>
      </symbol>
      <symbol id="sm-ef">
         <name>__TI_auto_init_nobinit_nopinit</name>
         <value>0x689</value>
         <object_component_ref idref="oc-92"/>
      </symbol>
      <symbol id="sm-f7">
         <name>_system_pre_init</name>
         <value>0x7fb</value>
         <object_component_ref idref="oc-69"/>
      </symbol>
      <symbol id="sm-102">
         <name>__TI_zero_init_nomemset</name>
         <value>0x7a1</value>
         <object_component_ref idref="oc-4b"/>
      </symbol>
      <symbol id="sm-10b">
         <name>__TI_decompress_none</name>
         <value>0x7b7</value>
         <object_component_ref idref="oc-20"/>
      </symbol>
      <symbol id="sm-116">
         <name>__TI_decompress_lzss</name>
         <value>0x48d</value>
         <object_component_ref idref="oc-1c"/>
      </symbol>
      <symbol id="sm-120">
         <name>abort</name>
         <value>0x7ed</value>
         <object_component_ref idref="oc-8b"/>
      </symbol>
      <symbol id="sm-12a">
         <name>HOSTexit</name>
         <value>0x7f3</value>
         <object_component_ref idref="oc-bc"/>
      </symbol>
      <symbol id="sm-12b">
         <name>C$$EXIT</name>
         <value>0x7f2</value>
         <object_component_ref idref="oc-bc"/>
      </symbol>
      <symbol id="sm-131">
         <name>__aeabi_memcpy</name>
         <value>0x7e5</value>
         <object_component_ref idref="oc-44"/>
      </symbol>
      <symbol id="sm-132">
         <name>__aeabi_memcpy4</name>
         <value>0x7e5</value>
         <object_component_ref idref="oc-44"/>
      </symbol>
      <symbol id="sm-133">
         <name>__aeabi_memcpy8</name>
         <value>0x7e5</value>
         <object_component_ref idref="oc-44"/>
      </symbol>
      <symbol id="sm-14c">
         <name>memcpy</name>
         <value>0x361</value>
         <object_component_ref idref="oc-7d"/>
      </symbol>
      <symbol id="sm-14d">
         <name>__TI_static_base__</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-150">
         <name>__mpu_init</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-151">
         <name>_system_post_cinit</name>
         <value>0x0</value>
      </symbol>
   </symbol_table>
   <title>Link successful</title>
</link_info>
