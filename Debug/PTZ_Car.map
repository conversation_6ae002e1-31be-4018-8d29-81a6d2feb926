******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Wed Jul 30 11:25:56 2025

OUTPUT FILE NAME:   <PTZ_Car.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 000006fd


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00000870  0001f790  R  X
  SRAM                  20200000   00008000  0000020c  00007df4  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00000870   00000870    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00000748   00000748    r-x .text
  00000808    00000808    00000038   00000038    r-- .rodata
  00000840    00000840    00000030   00000030    r-- .cinit
20200000    20200000    0000000c   00000000    rw-
  20200000    20200000    00000008   00000000    rw- .data
  20200008    20200008    00000004   00000000    rw- .bss
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00000748     
                  000000c0    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  000001c4    000000dc                 : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  000002a0    000000c0     main.o (.text.GROUP1_IRQHandler)
                  00000360    0000009a     libc.a : memcpy16.S.obj (.text:memcpy)
                  000003fa    00000002     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  000003fc    00000090     ti_msp_dl_config.o (.text.SYSCFG_DL_Motor_init)
                  0000048c    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00000508    00000078     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00000580    0000006c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  000005ec    0000005c     Motor.o (.text.Set_PWM_Duty)
                  00000648    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  0000068c    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  000006c8    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  000006fc    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00000724    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00000740    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  0000075c    00000018                 : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00000774    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  0000078c    00000018     main.o (.text.main)
                  000007a4    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  000007ba    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  000007cc    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  000007dc    0000000a                 : dl_common.o (.text.DL_Common_delayCycles)
                  000007e6    00000002     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  000007e8    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  000007f0    00000006     libc.a : exit.c.obj (.text:abort)
                  000007f6    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  000007fa    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  000007fe    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  00000802    00000006     --HOLE-- [fill = 0]

.cinit     0    00000840    00000030     
                  00000840    0000000c     (__TI_handler_table)
                  0000084c    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00000854    00000007     (.cinit..data.load) [load image, compression = lzss]
                  0000085b    00000001     --HOLE-- [fill = 0]
                  0000085c    00000010     (__TI_cinit_table)
                  0000086c    00000004     --HOLE-- [fill = 0]

.rodata    0    00000808    00000038     
                  00000808    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  00000830    00000008     ti_msp_dl_config.o (.rodata.gMotorConfig)
                  00000838    00000003     ti_msp_dl_config.o (.rodata.gMotorClockConfig)
                  0000083b    00000005     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.data      0    20200000    00000008     UNINITIALIZED
                  20200000    00000004     main.o (.data.gEncoderCount_L)
                  20200004    00000004     main.o (.data.gEncoderCount_R)

.bss       0    20200008    00000004     UNINITIALIZED
                  20200008    00000004     (.common:gpioB)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code   ro data   rw data
       ------                         ----   -------   -------
    .\
       ti_msp_dl_config.o             450    51        0      
       main.o                         216    0         12     
       startup_mspm0g350x_ticlang.o   6      192       0      
    +--+------------------------------+------+---------+---------+
       Total:                         672    243       12     
                                                              
    .\Hardware\
       Motor.o                        92     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         92     0         0      
                                                              
    C:/ti/mspm0_sdk_2_04_00_06/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                     356    0         0      
       dl_sysctl_mspm0g1x0x_g3x0x.o   288    0         0      
       dl_common.o                    10     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         654    0         0      
                                                              
    C:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       memcpy16.S.obj                 154    0         0      
       copy_decompress_lzss.c.obj     124    0         0      
       autoinit.c.obj                 60     0         0      
       boot_cortex_m.c.obj            40     0         0      
       copy_zero_init.c.obj           22     0         0      
       copy_decompress_none.c.obj     18     0         0      
       exit.c.obj                     6      0         0      
       pre_init.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         428    0         0      
                                                              
    C:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         4      0         0      
                                                              
    C:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       aeabi_memcpy.S.obj             8      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         8      0         0      
                                                              
       Stack:                         0      0         512    
       Linker Generated:              0      43        0      
    +--+------------------------------+------+---------+---------+
       Grand Total:                   1858   286       524    


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 0000085c records: 2, size/record: 8, table size: 16
	.bss: load addr=0000084c, load size=00000008 bytes, run addr=20200008, run size=00000004 bytes, compression=zero_init
	.data: load addr=00000854, load size=00000007 bytes, run addr=20200000, run size=00000008 bytes, compression=lzss


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00000840 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
000003fb  ADC0_IRQHandler                      
000003fb  ADC1_IRQHandler                      
000003fb  AES_IRQHandler                       
000007f6  C$$EXIT                              
000003fb  CANFD0_IRQHandler                    
000003fb  DAC0_IRQHandler                      
000007dd  DL_Common_delayCycles                
000001c5  DL_SYSCTL_configSYSPLL               
00000649  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
000000c1  DL_Timer_initFourCCPWMMode           
00000725  DL_Timer_setCaptCompUpdateMethod     
0000075d  DL_Timer_setCaptureCompareOutCtl     
000007cd  DL_Timer_setCaptureCompareValue      
00000741  DL_Timer_setClockConfig              
000003fb  DMA_IRQHandler                       
000003fb  Default_Handler                      
000003fb  GROUP0_IRQHandler                    
000002a1  GROUP1_IRQHandler                    
000007f7  HOSTexit                             
000003fb  HardFault_Handler                    
000003fb  I2C0_IRQHandler                      
000003fb  I2C1_IRQHandler                      
000003fb  NMI_Handler                          
000003fb  PendSV_Handler                       
000003fb  RTC_IRQHandler                       
000007fb  Reset_Handler                        
000003fb  SPI0_IRQHandler                      
000003fb  SPI1_IRQHandler                      
000003fb  SVC_Handler                          
00000509  SYSCFG_DL_GPIO_init                  
000003fd  SYSCFG_DL_Motor_init                 
00000581  SYSCFG_DL_SYSCTL_init                
000007e7  SYSCFG_DL_SYSTICK_init               
00000775  SYSCFG_DL_init                       
000006c9  SYSCFG_DL_initPower                  
000005ed  Set_PWM_Duty                         
000003fb  SysTick_Handler                      
000003fb  TIMA0_IRQHandler                     
000003fb  TIMA1_IRQHandler                     
000003fb  TIMG0_IRQHandler                     
000003fb  TIMG12_IRQHandler                    
000003fb  TIMG6_IRQHandler                     
000003fb  TIMG7_IRQHandler                     
000003fb  TIMG8_IRQHandler                     
000003fb  UART0_IRQHandler                     
000003fb  UART1_IRQHandler                     
000003fb  UART2_IRQHandler                     
000003fb  UART3_IRQHandler                     
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
0000085c  __TI_CINIT_Base                      
0000086c  __TI_CINIT_Limit                     
0000086c  __TI_CINIT_Warm                      
00000840  __TI_Handler_Table_Base              
0000084c  __TI_Handler_Table_Limit             
0000068d  __TI_auto_init_nobinit_nopinit       
0000048d  __TI_decompress_lzss                 
000007bb  __TI_decompress_none                 
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
000007a5  __TI_zero_init_nomemset              
000007e9  __aeabi_memcpy                       
000007e9  __aeabi_memcpy4                      
000007e9  __aeabi_memcpy8                      
ffffffff  __binit__                            
UNDEFED   __mpu_init                           
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
000006fd  _c_int00_noargs                      
UNDEFED   _system_post_cinit                   
000007ff  _system_pre_init                     
000007f1  abort                                
ffffffff  binit                                
20200000  gEncoderCount_L                      
20200004  gEncoderCount_R                      
20200008  gpioB                                
00000000  interruptVectors                     
0000078d  main                                 
00000361  memcpy                               


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  DL_Timer_initFourCCPWMMode           
000001c5  DL_SYSCTL_configSYSPLL               
00000200  __STACK_SIZE                         
000002a1  GROUP1_IRQHandler                    
00000361  memcpy                               
000003fb  ADC0_IRQHandler                      
000003fb  ADC1_IRQHandler                      
000003fb  AES_IRQHandler                       
000003fb  CANFD0_IRQHandler                    
000003fb  DAC0_IRQHandler                      
000003fb  DMA_IRQHandler                       
000003fb  Default_Handler                      
000003fb  GROUP0_IRQHandler                    
000003fb  HardFault_Handler                    
000003fb  I2C0_IRQHandler                      
000003fb  I2C1_IRQHandler                      
000003fb  NMI_Handler                          
000003fb  PendSV_Handler                       
000003fb  RTC_IRQHandler                       
000003fb  SPI0_IRQHandler                      
000003fb  SPI1_IRQHandler                      
000003fb  SVC_Handler                          
000003fb  SysTick_Handler                      
000003fb  TIMA0_IRQHandler                     
000003fb  TIMA1_IRQHandler                     
000003fb  TIMG0_IRQHandler                     
000003fb  TIMG12_IRQHandler                    
000003fb  TIMG6_IRQHandler                     
000003fb  TIMG7_IRQHandler                     
000003fb  TIMG8_IRQHandler                     
000003fb  UART0_IRQHandler                     
000003fb  UART1_IRQHandler                     
000003fb  UART2_IRQHandler                     
000003fb  UART3_IRQHandler                     
000003fd  SYSCFG_DL_Motor_init                 
0000048d  __TI_decompress_lzss                 
00000509  SYSCFG_DL_GPIO_init                  
00000581  SYSCFG_DL_SYSCTL_init                
000005ed  Set_PWM_Duty                         
00000649  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
0000068d  __TI_auto_init_nobinit_nopinit       
000006c9  SYSCFG_DL_initPower                  
000006fd  _c_int00_noargs                      
00000725  DL_Timer_setCaptCompUpdateMethod     
00000741  DL_Timer_setClockConfig              
0000075d  DL_Timer_setCaptureCompareOutCtl     
00000775  SYSCFG_DL_init                       
0000078d  main                                 
000007a5  __TI_zero_init_nomemset              
000007bb  __TI_decompress_none                 
000007cd  DL_Timer_setCaptureCompareValue      
000007dd  DL_Common_delayCycles                
000007e7  SYSCFG_DL_SYSTICK_init               
000007e9  __aeabi_memcpy                       
000007e9  __aeabi_memcpy4                      
000007e9  __aeabi_memcpy8                      
000007f1  abort                                
000007f6  C$$EXIT                              
000007f7  HOSTexit                             
000007fb  Reset_Handler                        
000007ff  _system_pre_init                     
00000840  __TI_Handler_Table_Base              
0000084c  __TI_Handler_Table_Limit             
0000085c  __TI_CINIT_Base                      
0000086c  __TI_CINIT_Limit                     
0000086c  __TI_CINIT_Warm                      
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
20200000  gEncoderCount_L                      
20200004  gEncoderCount_R                      
20200008  gpioB                                
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[93 symbols]
