#include "main.h"


int main(void)
{
    SYSCFG_DL_init();
    NVIC_EnableIRQ(GPIOB_INT_IRQn); // 编码器外部中断
    Set_PWM_Duty(0, 80);
    
    while (1) 
    {
      Set_PWM_Duty(0, 80);
    }
}

//编码器相关变量

volatile uint32_t gpioB;
int32_t gEncoderCount_L = 0;
int32_t gEncoderCount_R = 0;

void GROUP1_IRQHandler(void) {
  // 获取中断信号
  gpioB = DL_GPIO_getEnabledInterruptStatus(GPIOB, DL_GPIO_PIN_13|DL_GPIO_PIN_14|DL_GPIO_PIN_15|DL_GPIO_PIN_16);

  // 左轮
  if (gpioB & DL_GPIO_PIN_13) {
    if (!DL_GPIO_readPins(GPIOB, DL_GPIO_PIN_14)) {
      gEncoderCount_L--;
    } else {
      gEncoderCount_L++;
    }
    DL_GPIO_clearInterruptStatus(GPIOB, DL_GPIO_PIN_13);
  }
  if (gpioB & DL_GPIO_PIN_14) {
    if (!DL_GPIO_readPins(GPIOB, DL_GPIO_PIN_13)) {
      gEncoderCount_L++;
    } else {
      gEncoderCount_L--;
    }
    DL_GPIO_clearInterruptStatus(GPIOB, DL_GPIO_PIN_14);
  }

  // 右轮
  if (gpioB & DL_GPIO_PIN_15) {
    if (!DL_GPIO_readPins(GPIOB, DL_GPIO_PIN_16)) {
      gEncoderCount_R--;
      
    } else {
      gEncoderCount_R++;
      
    }
    DL_GPIO_clearInterruptStatus(GPIOB, DL_GPIO_PIN_15);
  }
  if (gpioB & DL_GPIO_PIN_16) {
    if (!DL_GPIO_readPins(GPIOB, DL_GPIO_PIN_15)) {
      gEncoderCount_R++;
      
    } else {
      
      gEncoderCount_R--;
    }
    DL_GPIO_clearInterruptStatus(GPIOB, DL_GPIO_PIN_16);
  }
}