#include "Motor.h"


void Set_PWM_Duty(uint8_t channel, int32_t duty)
{

  uint16_t cmp_val = abs(duty) * 400 / 100;

  if (channel == 0) {
    if (duty >= 0) { // 正转
      DL_GPIO_setPins(GPIOB, DL_GPIO_PIN_1);
      DL_GPIO_clearPins(GPIOB, DL_GPIO_PIN_0);
    } else { // 反转
      DL_GPIO_clearPins(GPIOB, DL_GPIO_PIN_0);
      DL_GPIO_setPins(GPIOB, DL_GPIO_PIN_1);
    }
    DL_TimerG_setCaptureCompareValue(Motor_INST, cmp_val, GPIO_Motor_C0_IDX);
    }
  else if (channel == 1) {
    if (duty >= 0) { // 正转
      DL_GPIO_setPins(GPIOB, DL_GPIO_PIN_3);
      DL_GPIO_clearPins(GPIOB, DL_GPIO_PIN_2);
    } else { // 反转
      DL_GPIO_clearPins(GPIOB, DL_GPIO_PIN_2);
      DL_GPIO_setPins(GPIOB, DL_GPIO_PIN_3);
    }
    DL_TimerG_setCaptureCompareValue(Motor_INST, cmp_val, GPIO_Motor_C1_IDX);
  }
    

}